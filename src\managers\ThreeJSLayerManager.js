/**
 * Three.js自定义图层管理器
 * 基于高德地图2.0+版本的GLCustomLayer实现3D模型渲染
 * 用于替代原来的Object3DLayer实现
 */

import * as THREE from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'

export class ThreeJSLayerManager {
  constructor(map, AMap) {
    this.map = map
    this.AMap = AMap
    
    // Three.js相关对象
    this.scene = null
    this.camera = null
    this.renderer = null
    this.gltfLoader = null
    
    // 自定义图层
    this.glCustomLayer = null
    
    // 数据转换工具
    this.customCoords = null
    
    // 模型管理
    this.models = new Map() // 模型ID -> Three.js对象
    this.modelPool = [] // 对象池
    this.maxPoolSize = 50
    
    // 状态管理
    this.isInitialized = false
    this.isDestroyed = false
    
    console.log('ThreeJSLayerManager 初始化')
  }

  /**
   * 初始化Three.js图层
   * @returns {Promise<boolean>} 初始化是否成功
   */
  async initialize() {
    try {
      console.log('开始初始化Three.js图层...')

      // 检查WebGL支持
      if (!this.checkWebGLSupport()) {
        console.error('浏览器不支持WebGL或WebGL已被禁用')
        return false
      }

      // 检查高德地图2.0+版本API支持
      if (!this.AMap.GLCustomLayer) {
        console.error('当前高德地图版本不支持GLCustomLayer')
        return false
      }

      // 获取数据转换工具
      this.customCoords = this.map.customCoords
      if (!this.customCoords) {
        console.error('无法获取customCoords数据转换工具')
        return false
      }

      // 创建GLCustomLayer
      this.glCustomLayer = new this.AMap.GLCustomLayer({
        zIndex: 10,
        init: this.onLayerInit.bind(this),
        render: this.onLayerRender.bind(this)
      })

      // 添加图层到地图
      this.map.add(this.glCustomLayer)

      this.isInitialized = true
      console.log('✅ Three.js图层初始化成功')
      return true

    } catch (error) {
      console.error('❌ Three.js图层初始化失败:', error)
      console.error('错误详情:', error.message)
      return false
    }
  }

  /**
   * 检查WebGL支持
   * @returns {boolean} 是否支持WebGL
   */
  checkWebGLSupport() {
    try {
      // 创建临时canvas检查WebGL支持
      const canvas = document.createElement('canvas')

      // 首先检查WebGL 2支持
      const webgl2Context = canvas.getContext('webgl2')
      if (webgl2Context) {
        console.log('✅ 浏览器支持WebGL 2')
        return true
      }

      // 检查WebGL 1支持
      const webglContext = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
      if (webglContext) {
        console.warn('⚠️ 浏览器仅支持WebGL 1，可能存在兼容性问题')
        console.warn('建议升级浏览器或显卡驱动以获得WebGL 2支持')
        return true
      }

      console.error('❌ 浏览器不支持WebGL')
      return false

    } catch (error) {
      console.error('检查WebGL支持时出错:', error)
      return false
    }
  }

  /**
   * GLCustomLayer初始化回调
   * @param {WebGLRenderingContext} gl - WebGL上下文
   */
  onLayerInit(gl) {
    try {
      console.log('GLCustomLayer初始化回调')
      console.log('WebGL上下文信息:', {
        version: gl.getParameter(gl.VERSION),
        vendor: gl.getParameter(gl.VENDOR),
        renderer: gl.getParameter(gl.RENDERER),
        shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION)
      })

      // 检查WebGL版本兼容性
      const isWebGL2 = gl instanceof WebGL2RenderingContext
      console.log('WebGL版本检查:', isWebGL2 ? 'WebGL 2' : 'WebGL 1')
      console.log('Three.js版本: 0.142.0 (官方示例兼容版本)')

      // 创建透视相机（3D地图模式）
      this.camera = new THREE.PerspectiveCamera(
        60,
        window.innerWidth / window.innerHeight,
        100,
        1 << 30
      )

      // 创建WebGL渲染器，使用高德地图提供的WebGL上下文
      this.renderer = new THREE.WebGLRenderer({
        context: gl,
        antialias: true,
        alpha: true,
        depth: true,
        stencil: true,
        preserveDrawingBuffer: false
      })

      // 重要：禁用自动清空画布，否则地图底图无法显示
      this.renderer.autoClear = false

      // 设置渲染器参数
      this.renderer.setSize(window.innerWidth, window.innerHeight)
      this.renderer.setPixelRatio(window.devicePixelRatio)

      // 创建场景
      this.scene = new THREE.Scene()

      // 添加环境光和平行光
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.3)
      const directionalLight = new THREE.DirectionalLight(0xffffff, 1)
      directionalLight.position.set(1000, -100, 900)

      this.scene.add(ambientLight)
      this.scene.add(directionalLight)

      // 创建GLTF加载器
      this.gltfLoader = new GLTFLoader()

      console.log('✅ Three.js场景初始化完成')
      console.log('渲染器信息:', {
        webglVersion: this.renderer.capabilities.isWebGL2 ? 'WebGL 2' : 'WebGL 1',
        maxTextures: this.renderer.capabilities.maxTextures,
        maxVertexTextures: this.renderer.capabilities.maxVertexTextures,
        maxTextureSize: this.renderer.capabilities.maxTextureSize
      })

    } catch (error) {
      console.error('❌ GLCustomLayer初始化失败:', error)
      console.error('错误详情:', error.stack)

      // 提供详细的错误诊断信息
      console.error('💡 错误诊断:')
      console.error('1. Three.js版本: 0.162.0')
      console.error('2. WebGL上下文类型:', gl.constructor.name)
      console.error('3. 浏览器信息:', navigator.userAgent)
      console.error('4. 建议检查显卡驱动是否最新')
    }
  }

  /**
   * GLCustomLayer渲染回调
   */
  onLayerRender() {
    try {
      if (!this.renderer || !this.scene || !this.camera || !this.customCoords) {
        return
      }

      // 检查地图是否为3D模式
      const viewMode = this.map.getViewMode ? this.map.getViewMode() : (this.map.getViewMode_ ? this.map.getViewMode_() : '2D')
      if (viewMode !== '3D') {
        // 如果不是3D模式，不进行渲染
        return
      }

      // 重置Three.js的GL上下文状态
      this.renderer.resetState()

      // 重新设置图层的渲染中心点 - 按照官方示例的方式
      // 使用云南省中心坐标作为渲染中心
      this.customCoords.setCenter([101.5, 25.0])

      // 获取相机参数
      let cameraParams
      try {
        cameraParams = this.customCoords.getCameraParams()
      } catch (error) {
        console.warn('获取相机参数失败:', error)
        return
      }

      // 检查相机参数是否有效
      if (!cameraParams) {
        return
      }

      // 安全地解构参数
      const { near, far, fov, up, lookAt, position } = cameraParams

      // 检查必要参数是否存在
      if (near === undefined || far === undefined || fov === undefined ||
          !position || !Array.isArray(position) || position.length < 3) {
        return
      }

      // 更新相机参数
      this.camera.near = near
      this.camera.far = far
      this.camera.fov = fov
      this.camera.position.set(position[0], position[1], position[2])

      // 安全地设置up向量
      if (up && Array.isArray(up) && up.length >= 3) {
        this.camera.up.set(up[0], up[1], up[2])
      } else {
        // 使用默认up向量
        this.camera.up.set(0, 1, 0)
      }

      // 安全地设置lookAt
      if (lookAt && Array.isArray(lookAt) && lookAt.length >= 3) {
        this.camera.lookAt(lookAt[0], lookAt[1], lookAt[2])
      } else {
        // 使用默认lookAt
        this.camera.lookAt(0, 0, 0)
      }

      this.camera.updateProjectionMatrix()

      // 渲染场景
      this.renderer.render(this.scene, this.camera)

      // 重置GL状态
      this.renderer.resetState()

    } catch (error) {
      console.error('❌ Three.js渲染失败:', error)
    }
  }

  /**
   * 加载GLTF模型
   * @param {string} modelPath - 模型路径
   * @returns {Promise<THREE.Group>} 加载的模型对象
   */
  async loadGLTFModel(modelPath) {
    return new Promise((resolve, reject) => {
      this.gltfLoader.load(
        modelPath,
        (gltf) => {
          console.log('✅ GLTF模型加载成功:', modelPath)
          resolve(gltf.scene)
        },
        (progress) => {
          // 加载进度
          console.log('GLTF模型加载进度:', (progress.loaded / progress.total * 100) + '%')
        },
        (error) => {
          console.error('❌ GLTF模型加载失败:', error)
          reject(error)
        }
      )
    })
  }

  /**
   * 添加3D模型到场景
   * @param {string} modelId - 模型唯一标识
   * @param {THREE.Object3D} model - Three.js模型对象
   * @param {Array} position - 经纬度位置 [lng, lat]
   * @param {Object} options - 其他选项
   */
  addModel(modelId, model, position, options = {}) {
    try {
      if (!this.scene || !this.customCoords) {
        console.error('场景或坐标转换工具未初始化')
        return false
      }
      
      // 转换经纬度坐标到Three.js坐标系
      const coords = this.customCoords.lngLatsToCoords([position])
      if (!coords || coords.length === 0) {
        console.error('坐标转换失败:', position)
        return false
      }
      
      const [x, y] = coords[0]
      
      // 设置模型位置
      model.position.set(x, y, options.height || 0)
      
      // 设置模型缩放
      if (options.scale) {
        model.scale.set(options.scale, options.scale, options.scale)
      }
      
      // 设置模型旋转
      if (options.rotation) {
        model.rotation.set(
          options.rotation.x || 0,
          options.rotation.y || 0,
          options.rotation.z || 0
        )
      }
      
      // 添加到场景
      this.scene.add(model)
      
      // 记录模型
      this.models.set(modelId, {
        model: model,
        position: position,
        options: options
      })
      
      console.log(`✅ 模型已添加到场景: ${modelId}`)
      return true
      
    } catch (error) {
      console.error(`❌ 添加模型失败 ${modelId}:`, error)
      return false
    }
  }

  /**
   * 从场景中移除模型
   * @param {string} modelId - 模型唯一标识
   */
  removeModel(modelId) {
    try {
      const modelData = this.models.get(modelId)
      if (!modelData) {
        console.warn(`模型不存在: ${modelId}`)
        return false
      }
      
      // 从场景中移除
      this.scene.remove(modelData.model)
      
      // 将模型返回到对象池
      this.returnModelToPool(modelData.model)
      
      // 从记录中删除
      this.models.delete(modelId)
      
      console.log(`✅ 模型已移除: ${modelId}`)
      return true
      
    } catch (error) {
      console.error(`❌ 移除模型失败 ${modelId}:`, error)
      return false
    }
  }

  /**
   * 将模型返回到对象池
   * @param {THREE.Object3D} model - 模型对象
   */
  returnModelToPool(model) {
    try {
      if (this.modelPool.length < this.maxPoolSize) {
        // 重置模型状态
        model.position.set(0, 0, 0)
        model.rotation.set(0, 0, 0)
        model.scale.set(1, 1, 1)
        
        this.modelPool.push(model)
        console.log(`♻️ 模型已返回对象池，当前池大小: ${this.modelPool.length}`)
      } else {
        // 对象池已满，销毁模型
        this.disposeModel(model)
        console.log('📦 对象池已满，模型已销毁')
      }
    } catch (error) {
      console.error('❌ 返回模型到对象池失败:', error)
    }
  }

  /**
   * 从对象池获取模型
   * @returns {THREE.Object3D|null} 模型对象
   */
  getModelFromPool() {
    if (this.modelPool.length > 0) {
      const model = this.modelPool.pop()
      console.log(`♻️ 从对象池获取模型，剩余: ${this.modelPool.length}`)
      return model
    }
    return null
  }

  /**
   * 销毁模型对象
   * @param {THREE.Object3D} model - 模型对象
   */
  disposeModel(model) {
    try {
      model.traverse((child) => {
        if (child.geometry) {
          child.geometry.dispose()
        }
        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach(material => material.dispose())
          } else {
            child.material.dispose()
          }
        }
      })
      console.log('模型资源已释放')
    } catch (error) {
      console.error('销毁模型失败:', error)
    }
  }

  /**
   * 清空所有模型
   */
  clearAllModels() {
    try {
      console.log('开始清空所有模型...')
      
      // 移除场景中的所有模型
      this.models.forEach((modelData, modelId) => {
        this.scene.remove(modelData.model)
        this.disposeModel(modelData.model)
      })
      
      // 清空记录
      this.models.clear()
      
      // 清空对象池
      this.modelPool.forEach(model => this.disposeModel(model))
      this.modelPool = []
      
      console.log('✅ 所有模型已清空')
      
    } catch (error) {
      console.error('❌ 清空模型失败:', error)
    }
  }

  /**
   * 窗口大小变化处理
   */
  onWindowResize() {
    if (this.camera && this.renderer) {
      this.camera.aspect = window.innerWidth / window.innerHeight
      this.camera.updateProjectionMatrix()
      this.renderer.setSize(window.innerWidth, window.innerHeight)
    }
  }

  /**
   * 销毁图层管理器
   */
  destroy() {
    try {
      console.log('开始销毁Three.js图层管理器...')
      
      this.isDestroyed = true
      
      // 清空所有模型
      this.clearAllModels()
      
      // 移除图层
      if (this.glCustomLayer && this.map) {
        this.map.remove(this.glCustomLayer)
      }
      
      // 清理Three.js资源
      if (this.renderer) {
        this.renderer.dispose()
      }
      
      // 清理引用
      this.scene = null
      this.camera = null
      this.renderer = null
      this.gltfLoader = null
      this.glCustomLayer = null
      this.customCoords = null
      this.map = null
      this.AMap = null
      
      console.log('✅ Three.js图层管理器已销毁')
      
    } catch (error) {
      console.error('❌ 销毁Three.js图层管理器失败:', error)
    }
  }

  /**
   * 获取管理器状态
   * @returns {Object} 状态信息
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      isDestroyed: this.isDestroyed,
      modelCount: this.models.size,
      poolSize: this.modelPool.length,
      hasScene: !!this.scene,
      hasCamera: !!this.camera,
      hasRenderer: !!this.renderer
    }
  }
}

export default ThreeJSLayerManager
